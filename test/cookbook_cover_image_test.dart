import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/widgets/cookbook_cover_image.dart';

void main() {
  group('CookbookCoverImage Tests', () {
    testWidgets('should display cover image when coverImageUrl is provided', (WidgetTester tester) async {
      // Create a cookbook with coverImageUrl
      final cookbook = Cookbook(
        id: 1,
        name: 'Test Cookbook',
        recipeCount: 5,
        system: false,
        dateAdded: DateTime.now(),
        lastModified: DateTime.now(),
        coverImageUrl: 'https://example.com/cover.jpg',
        recipeMedia: null,
        fromStore: false,
        type: 'user',
        readOnly: false,
        permission: 'owner',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CookbookCoverImage(
              cookbook: cookbook,
              height: 200,
              width: 200,
            ),
          ),
        ),
      );

      // Verify that the widget is rendered
      expect(find.byType(CookbookCoverImage), findsOneWidget);
    });

    testWidgets('should display collage when coverImageUrl is null but recipeMedia is provided', (WidgetTester tester) async {
      // Create a cookbook with null coverImageUrl but with recipeMedia
      final cookbook = Cookbook(
        id: 1,
        name: 'Test Cookbook',
        recipeCount: 5,
        system: false,
        dateAdded: DateTime.now(),
        lastModified: DateTime.now(),
        coverImageUrl: null,
        recipeMedia: [
          CookbookRecipeMedia(url: 'https://example.com/recipe1.jpg'),
          CookbookRecipeMedia(url: 'https://example.com/recipe2.jpg'),
          CookbookRecipeMedia(url: 'https://example.com/recipe3.jpg'),
        ],
        fromStore: false,
        type: 'user',
        readOnly: false,
        permission: 'owner',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CookbookCoverImage(
              cookbook: cookbook,
              height: 200,
              width: 200,
            ),
          ),
        ),
      );

      // Verify that the widget is rendered
      expect(find.byType(CookbookCoverImage), findsOneWidget);
    });

    testWidgets('should display placeholder when both coverImageUrl and recipeMedia are null/empty', (WidgetTester tester) async {
      // Create a cookbook with null coverImageUrl and empty recipeMedia
      final cookbook = Cookbook(
        id: 1,
        name: 'Test Cookbook',
        recipeCount: 5,
        system: false,
        dateAdded: DateTime.now(),
        lastModified: DateTime.now(),
        coverImageUrl: null,
        recipeMedia: [],
        fromStore: false,
        type: 'user',
        readOnly: false,
        permission: 'owner',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CookbookCoverImage(
              cookbook: cookbook,
              height: 200,
              width: 200,
            ),
          ),
        ),
      );

      // Verify that the widget is rendered
      expect(find.byType(CookbookCoverImage), findsOneWidget);
    });
  });

  group('CookbookRecipeMedia Tests', () {
    test('should create CookbookRecipeMedia from JSON', () {
      final json = {'url': 'https://example.com/image.jpg'};
      final media = CookbookRecipeMedia.fromJson(json);
      
      expect(media.url, equals('https://example.com/image.jpg'));
    });

    test('should convert CookbookRecipeMedia to JSON', () {
      final media = CookbookRecipeMedia(url: 'https://example.com/image.jpg');
      final json = media.toJson();
      
      expect(json['url'], equals('https://example.com/image.jpg'));
    });
  });

  group('Cookbook Model Tests', () {
    test('should parse recipeMedia from JSON', () {
      final json = {
        'id': 1,
        'name': 'Test Cookbook',
        'recipeCount': 5,
        'system': false,
        'dateAdded': '2023-01-01T00:00:00.000Z',
        'lastModified': '2023-01-01T00:00:00.000Z',
        'coverImageUrl': null,
        'recipeMedia': [
          {'url': 'https://example.com/recipe1.jpg'},
          {'url': 'https://example.com/recipe2.jpg'},
        ],
        'fromStore': false,
        'type': 'user',
        'readOnly': false,
        'permission': 'owner',
      };

      final cookbook = Cookbook.fromJson(json);
      
      expect(cookbook.recipeMedia, isNotNull);
      expect(cookbook.recipeMedia!.length, equals(2));
      expect(cookbook.recipeMedia![0].url, equals('https://example.com/recipe1.jpg'));
      expect(cookbook.recipeMedia![1].url, equals('https://example.com/recipe2.jpg'));
    });

    test('should handle null recipeMedia in JSON', () {
      final json = {
        'id': 1,
        'name': 'Test Cookbook',
        'recipeCount': 5,
        'system': false,
        'dateAdded': '2023-01-01T00:00:00.000Z',
        'lastModified': '2023-01-01T00:00:00.000Z',
        'coverImageUrl': 'https://example.com/cover.jpg',
        'recipeMedia': null,
        'fromStore': false,
        'type': 'user',
        'readOnly': false,
        'permission': 'owner',
      };

      final cookbook = Cookbook.fromJson(json);
      
      expect(cookbook.recipeMedia, isNull);
      expect(cookbook.coverImageUrl, equals('https://example.com/cover.jpg'));
    });
  });
}
