import 'package:flutter_test/flutter_test.dart';
import 'package:mastercookai/core/utils/version_utils.dart';

void main() {
  group('VersionUtils Tests', () {
    test('compareVersions should work correctly', () {
      // Test equal versions
      expect(VersionUtils.compareVersions('1.0.0', '1.0.0'), 0);
      expect(VersionUtils.compareVersions('1.4.0', '1.4.0'), 0);
      
      // Test current version less than API version
      expect(VersionUtils.compareVersions('1.0.0', '1.1.0'), -1);
      expect(VersionUtils.compareVersions('1.4.0', '1.5.0'), -1);
      expect(VersionUtils.compareVersions('1.4.0', '2.0.0'), -1);
      
      // Test current version greater than API version
      expect(VersionUtils.compareVersions('1.1.0', '1.0.0'), 1);
      expect(VersionUtils.compareVersions('1.5.0', '1.4.0'), 1);
      expect(VersionUtils.compareVersions('2.0.0', '1.4.0'), 1);
    });
    
    test('isUpdateRequired should work correctly', () {
      // Update required
      expect(VersionUtils.isUpdateRequired('1.0.0', '1.1.0'), true);
      expect(VersionUtils.isUpdateRequired('1.4.0', '1.5.0'), true);
      expect(VersionUtils.isUpdateRequired('1.4.0', '2.0.0'), true);
      
      // Update not required
      expect(VersionUtils.isUpdateRequired('1.0.0', '1.0.0'), false);
      expect(VersionUtils.isUpdateRequired('1.1.0', '1.0.0'), false);
      expect(VersionUtils.isUpdateRequired('1.5.0', '1.4.0'), false);
    });
  });
}
