name: mastercookai
description: "A new Flutter project."
publish_to: 'none'
version: 1.4.0+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  flutter_dotenv: ^5.2.1
  window_size: ^0.1.0
  cupertino_icons: ^1.0.8
  hooks_riverpod: ^2.6.1  # Use hooks_riverpod instead of flutter_riverpod
  flutter_hooks: ^0.21.1-pre.4
  http: ^1.2.0
  go_router: ^15.1.2
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.3.0
  iconsax_plus: ^1.0.0
  flutter_launcher_icons: ^0.14.3
  flutter_remix: ^0.0.3
  flutter_svg: ^2.0.17
  date_format: ^2.0.9
  table_calendar: ^3.0.9
  flutter_inappwebview: ^6.1.5
  url_launcher: ^6.3.1
  responsive_framework: ^1.5.1
  internet_connection_checker_plus: ^2.7.2
  dio: ^5.4.0
  device_info_plus: ^11.3.0
  connectivity_plus: ^6.0.3
  universal_platform: ^1.1.0
  shared_preferences: ^2.5.3
  wolt_modal_sheet: ^0.11.0
  loading_animation_widget: ^1.3.0
  flutter_image_compress: ^2.3.0
  file_picker: ^10.2.0 # Downgraded to match earlier recommendation
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  http_parser: ^4.0.2
  fluttertoast: ^8.2.12
  intl: ^0.19.0
  uuid: ^4.5.1
  webview_flutter_web: ^0.2.3+4
  timeago: ^3.6.0
  dotted_border: ^2.1.0
  shimmer: ^3.0.0
  riverpod_annotation: ^2.3.5  # Align with hooks_riverpod
  freezed_annotation: ^2.4.4  # Corrected version
  toastification: ^3.0.3
  flutter_smart_dialog: ^4.9.8+8
  another_flushbar: ^1.12.30
  numberpicker: ^2.1.2
  video_compress: ^3.1.4
  video_thumbnail: ^0.5.6
  video_player: ^2.10.0
  # Enhanced video player for Windows/Desktop
  media_kit: ^1.2.0
  media_kit_video: ^1.3.0
  media_kit_libs_video: ^1.0.6
  paged_grid_view: ^0.0.4
 #  ffmpeg_kit_flutter: ^6.0.3
  flutter_staggered_grid_view: ^0.7.0
  responsive_grid: ^2.4.4
  responsive_grid_list: ^1.4.0
  overlay_menu: ^0.1.0
  hover_menu: ^1.1.2
  dropdown_button2: ^2.3.9
  google_fonts: ^6.2.1
  pin_code_fields: ^8.0.1
  intl_phone_field: ^3.2.0
  percent_indicator: ^4.2.5
  flexible_wrap: ^1.0.6
  carousel_slider: ^5.1.1
  floating_bottom_bar: ^0.0.3
  circular_menu: ^4.0.0
  country_pickers: ^3.0.1
  crop_image: ^1.0.16
  custom_image_crop: ^0.1.1
  path_provider: ^2.1.5
  share_plus: ^11.0.0
  lottie: ^3.1.2
  figma_layout_grid: ^0.3.2
  card_banner: ^0.0.1+2
  in_app_purchase: ^3.2.3
  collection: ^1.18.0
  in_app_purchase_storekit: ^0.4.4
  permission_handler: ^12.0.1
  window_manager: ^0.3.0
  package_info_plus: ^8.3.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.9  # Compatible with riverpod_generator
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.4.0  # Pinned to avoid uuid conflict
  freezed: ^2.4.5  # Matches freezed_annotation


flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/home_icons/
    - assets/cookbook/
    - assets/icons/
    - assets/shopping/
    - assets/profile/
    - assets/mealplan/
    - assets/access_keys.json
    - assets/MasterIngredients.json
    - assets/NoData.json
    #- .env
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Light.ttf
        - asset: assets/fonts/Inter-Medium.ttf
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-SemiBold.ttf
        - asset: assets/fonts/Inter-Bold.ttf
        - asset: assets/fonts/Inter-ExtraBold.ttf
