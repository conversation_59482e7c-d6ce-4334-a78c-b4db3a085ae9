import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../../app/imports/packages_imports.dart';
import 'dart:io' show Platform;
import '../../../core/utils/device_utils.dart';
import '../../../core/widgets/YouTubeLauncher.dart';
import '../../../core/widgets/YouTubePlayer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text_button.dart';
import '../../core/network/app_status.dart';
import '../../core/providers/auth/controllers/auth_notifier.dart';
import '../../core/utils/Utils.dart';
import '../../core/utils/validator.dart';
import 'signup_screen.dart';
import 'dialogs/password_reset_dialog.dart';
import 'mobile/mobile_welcome_screen.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final isHovered = useState(false);

    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final formKeyForgot = useMemoized(() => GlobalKey<FormState>());


    return getDeviceType(context).name == 'mobile'
        ? MobileWelcomeScreen()
        : Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              children: [
                Positioned.fill(
                  child: Image.asset(
                    AssetsManager.background_img,
                    fit: BoxFit.cover,
                  ),
                ),
                Center(
                  child: FractionallySizedBox(
                    widthFactor:
                        DeviceUtils().isTabletOrIpad(context) ? 0.8 : 0.45,
                    heightFactor: 0.62,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.95),
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      child: Row(
                        children: [
                          // Left Section
                          Visibility(
                            visible: getDeviceType(context).name == 'mobile'
                                ? false
                                : true,
                            child: Expanded(
                              child: Container(
                                padding: EdgeInsets.all(16.w),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.r),
                                    bottomLeft: Radius.circular(30.r),
                                  ),
                                  image: DecorationImage(
                                    image:
                                        AssetImage(AssetsManager.recipe_bg_img),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(top: 50),
                                      child: Image.asset(
                                        AssetsManager.mastercook_ai_logo,
                                        height: 250.h,
                                        width: 400.w,
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 70),
                                      child: Align(
                                        alignment: Alignment.bottomCenter,
                                        child: CustomText(
                                          text: "Watch getting started video",
                                          fontFamily: 'Inter',
                                          size: 16,
                                          weight: FontWeight.w400,
                                          height: 16 / 12,
                                          color: AppColors.primaryGreyColor,
                                          align: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: 10.0, left: 20.0, right: 20.0),
                                      child: Container(
                                        width: 338,
                                        height: 180,
                                        decoration: BoxDecoration(
                                          color: Color(0xFF333333),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            child: (kIsWeb || Platform.isMacOS)
                                                ? const InAppYouTubePlayer(
                                                    videoId: "d1q4nwMUegA")
                                                : (Platform.isWindows ||
                                                        Platform.isLinux)
                                                    ? YouTubeLauncher()
                                                    : InAppYouTubePlayer(
                                                        videoId: "d1q4nwMUegA")
                                            // Center(
                                            //   child: Icon(
                                            //     Icons.play_circle_fill,
                                            //     size: 48,
                                            //     color: Colors.white.withValues(alpha: 0.8),
                                            //   ),
                                            //
                                            // ),
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Right Section
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.all(18.w),
                              child: Form(
                                key: formKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(height: 20.h),
                                    RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: responsiveFont(18).sp,
                                          height: 16 / 12,
                                          letterSpacing: 0,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: "Don’t have an Account? ",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF4F4F4F),
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          TextSpan(
                                            text: "Register now",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Color(0xFF007AFF),
                                              fontWeight: FontWeight.w400,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) =>
                                                          const SignupScreen()),
                                                );
                                                //Utils().launchURL(url: 'https://mastercook.ai/');
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(),
                                    CustomText(
                                      text: "Welcome Back!",
                                      color: AppColors.primaryGreyColor,
                                      size:
                                          DeviceUtils().isTabletOrIpad(context)
                                              ? 24
                                              : responsiveFont(40.sp),
                                      weight: FontWeight.w700,
                                      fontFamily: 'Inter',
                                    ),
                                    SizedBox(height: 100),
                                    Container(
                                      margin:
                                          EdgeInsets.symmetric(horizontal: 50),
                                      child: Column(
                                        children: [
                                          CustomInputField(
                                            hintText: 'Username',
                                            // fontSize: DeviceUtils().isTabletOrIpad(context) ? 20 : responsiveFont(20).sp,
                                            controller: emailController,
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            //autoFocus: true,
                                            validator: Validator.validateEmail,
                                          ),
                                          SizedBox(height: 20.h),
                                          CustomInputField(
                                            hintText: 'Password',
                                            controller: passwordController,
                                            keyboardType:
                                                TextInputType.emailAddress,
                                            isPassword: true,
                                            validator:
                                                Validator.validatePassword,
                                          ),
                                          SizedBox(height: 20.h),
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: MouseRegion(
                                              cursor: SystemMouseCursors.click,
                                              onEnter: (_) =>
                                                  isHovered.value = true,
                                              onExit: (_) =>
                                                  isHovered.value = false,
                                              child: CustomTextButton(
                                                text: 'Forgot Password?',
                                                size: 14,
                                                color: AppColors
                                                    .primaryLightTextColor,
                                                onPressed: () {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) =>
                                                        passwordResetDialog(
                                                            context,
                                                            formKeyForgot:
                                                                formKeyForgot),
                                                  );
                                                },
                                                underline: false,
                                                hoverColor: isHovered.value
                                                    ? AppColors
                                                        .darkPrimaryBackgroundColor
                                                    : Colors.transparent,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 30.h),
                                          CustomButton(
                                            text: 'Login',
                                            height: 30,
                                            isLoading: authState.status ==
                                                AppStatus.loading,
                                            width: 250,
                                            onPressed: () {
                                              if (formKey.currentState!
                                                  .validate()) {
                                                authNotifier.authenticate(
                                                  context,
                                                  emailController.text.trim(),
                                                  passwordController.text
                                                      .trim(),
                                                );
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(),
                                    const Spacer(),
                                    // CustomText(
                                    //   text: "Love MasterCook?",
                                    //   color: AppColors.primaryLightTextColor,
                                    //   size: 20,
                                    //   weight: FontWeight.w600,
                                    // ),
                                    //SizedBox(height: 30.h),
                                    // SizedBox(
                                    //   height: 32,
                                    //   width: 181,
                                    //   child: CustomButton(
                                    //     onPressed: () {
                                    //       Utils().launchURL(
                                    //           url: 'https://mastercook.com/');
                                    //     },
                                    //     text: "Visit Website",
                                    //     height: 16 / 12,
                                    //     fontFamily: 'Inter',
                                    //     isUnderline: true,
                                    //   ),
                                    // ),
                                    SizedBox(height: 50.h),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}
