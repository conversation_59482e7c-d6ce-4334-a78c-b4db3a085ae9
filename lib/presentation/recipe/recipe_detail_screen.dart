import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/presentation/cookbook/widgets/cookbook_filter.dart';
import 'package:mastercookai/presentation/recipe/subview/recipe_cards.dart';
import 'package:mastercookai/presentation/recipe/tab/tab_recipe_detail.dart';
import '../../../../app/imports/packages_imports.dart';
import '../../../../core/widgets/custom_searchbar.dart';
import '../../../core/data/models/recipe_response.dart';
import '../../core/data/models/category_response.dart';
import '../../core/data/models/cuisines_response.dart';
import '../../core/data/models/plan_type.dart';
import '../../core/providers/categories_notifier.dart';
import '../../core/providers/nutrions_notifier.dart';
import '../../core/providers/profile/user_profile_notifier.dart';
import '../../core/providers/recipe/media_provider.dart';
import '../../core/providers/recipe/metadata_provider.dart';
import '../../core/providers/recipe_notifier.dart';
import '../../core/providers/single_recipe_notifier.dart';
import '../../core/providers/cuisines_notifier.dart';
import '../../core/utils/device_utils.dart';
import '../../core/widgets/custom_button.dart';
import '../../core/widgets/no_data_widget.dart';
import '../cookbook/widgets/drop_down_filter.dart';
import '../shimer/cookbook_list_shimmer.dart';
import '../shimer/recipe_detail_shimmer.dart';
import 'detail_subview/left_media_video_view.dart';
import 'detail_subview/responsive_right_view.dart';
import '../../core/widgets/custom_drawer.dart';
import 'mobile_ui/recipe_detail_screen_mobile.dart';

class RecipeDetailScreen extends ConsumerStatefulWidget {
  final int recipeId;
  final String selectedRecipeName;
  final int cookbookId;
  final List<Recipe> recipesList;
  final String cookBookName;

  const RecipeDetailScreen({
    super.key,
    required this.recipeId,
    required this.recipesList,
    required this.selectedRecipeName,
    required this.cookbookId,
    required this.cookBookName,
  });

  @override
  ConsumerState<RecipeDetailScreen> createState() => _RecipeDetailScreenState();
}

final Map<String, List<String>> _filterOptions = {
  "Cuisine": [],
  "Category": [],
};

class _RecipeDetailScreenState extends ConsumerState<RecipeDetailScreen> {
  Map<String, String> _selectedFilters = {
    "Cuisine": "",
    "Category": "",
  };
  final TextEditingController recipeSearchController = TextEditingController();
  String? selectedMediaUrl; // Renamed to reflect it could be video or image
  File? selectedMediaFile; // Added to track local media file
  bool mediaInitialized = false;
  final ScrollController _scrollController = ScrollController();
  int? _selectedRecipeId;
  String? selectedRecipeName;
  int? _selectedCookbookId;
  int _selectedRecipeIndex = 0;
  int cuisine = 0;
  int category = 0;
  int _currentPage = 1;
  Timer? _recipeSearchDebounce;

  @override
  void initState() {
    super.initState();
    selectedRecipeName = widget.selectedRecipeName;
    _selectedRecipeId = widget.recipeId;
    _selectedCookbookId = widget.cookbookId;
    recipeSearchController.addListener(_onSearchChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final recipeListState = ref.watch(recipeNotifierProvider);
      if (recipeListState.data != null && recipeListState.data!.isNotEmpty) {
        final index = recipeListState.data!
            .indexWhere((recipe) => recipe.id == widget.recipeId);
        if (index != -1) {
          setState(() {
            _selectedRecipeIndex = index;
            debugPrint(
                'Initialized: selected index=$_selectedRecipeIndex, recipeId=${widget.recipeId}');
          });
          scrollToSelectedRecipe(_selectedRecipeIndex);
        }
      }

      ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
            context: context,
            cookbookId: _selectedCookbookId ?? 0,
            recipeId: widget.recipeId,
          );
      var userProfile = ref.watch(userProfileNotifierProvider).data?.userProfile;

      if (userProfile!.currentPlan!.planName != PlanType.Free.name) {
        // Reset nutrition state before fetching for initial recipe
        ref.read(nutritionInfoNotifierProvider.notifier).resetNutritionState();
        ref.read(nutritionInfoNotifierProvider.notifier).getNutritions(
              context: context,
              recipeId: widget.recipeId,
            );
      }

      ref
          .read(cuisinesNotifierProvider.notifier)
          .fetchCuisines(context: context);
      ref
          .read(categoriesNotifierProvider.notifier)
          .fetchCategories(context: context);

      final cuisines = ref.read(cuisinesNotifierProvider).data ?? [];
      _filterOptions['Cuisine'] = cuisines.map((c) => c.name ?? '').toList();

      final categories = ref.read(categoriesNotifierProvider).data ?? [];
      _filterOptions['Category'] = categories.map((c) => c.name ?? '').toList();
    });
  }

  @override
  void dispose() {
    recipeSearchController.removeListener(_onSearchChanged);
    recipeSearchController.dispose();
    _recipeSearchDebounce?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void scrollToSelectedRecipe(int index) {
    if (_scrollController.hasClients) {
      const double itemHeight = 120.0;
      const double padding = 12.0;
      final recipeListState = ref.read(recipeNotifierProvider);
      if (recipeListState.data != null &&
          index < recipeListState.data!.length) {
        final offset = index * (itemHeight + padding);
        _scrollController.animateTo(
          offset.clamp(0.0, _scrollController.position.maxScrollExtent),
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
        debugPrint(
            'Scrolling to recipe index: $index, offset: $offset, list length: ${recipeListState.data!.length}');
      } else {
        debugPrint(
            'Invalid index: $index, list length: ${recipeListState.data?.length ?? 0}');
      }
    } else {
      debugPrint('ScrollController not attached');
    }
  }

  void _onSearchChanged() {
    final text = recipeSearchController.text.trim();
    debugPrint('Search query changed: $text');
    if (text.length >= 3 || text.isEmpty) {
      _debounceSearch(text);
    }
  }



  void _debounceSearch(String query) {
    _recipeSearchDebounce?.cancel();
    _recipeSearchDebounce = Timer(const Duration(milliseconds: 500), () async {
      debugPrint('Debounced search with query: $query');
      await ref.read(recipeNotifierProvider.notifier).fetchRecipes(
            cookbookId: _selectedCookbookId!,
            cuisineId: cuisine,
            categoryId: category,
            search: query.isEmpty ? null : query,
            reset: true,
            context: context,
            currentPage: 1,
          );
      if (context.mounted) {
        final recipeListState = ref.read(recipeNotifierProvider);
        if (recipeListState.data != null && recipeListState.data!.isNotEmpty) {
          final index = recipeListState.data!
              .indexWhere((recipe) => recipe.id == _selectedRecipeId);
          setState(() {
            _selectedRecipeIndex = index != -1 ? index : 0;
            _selectedRecipeId = recipeListState.data![_selectedRecipeIndex].id;
            selectedRecipeName =
                recipeListState.data![_selectedRecipeIndex].name;
            debugPrint(
                'Search updated: index=$_selectedRecipeIndex, recipeId=$_selectedRecipeId');
          });
          scrollToSelectedRecipe(_selectedRecipeIndex);
          ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
                context: context,
                cookbookId: _selectedCookbookId ?? 0,
                recipeId: _selectedRecipeId!,
              );
        } else {
          setState(() {
            _selectedRecipeId = null;
            selectedRecipeName = null;
            selectedMediaUrl = null;
            selectedMediaFile = null;
            debugPrint('No recipes found after search');
          });
        }
      }
    });
  }

  bool _hasActiveFilters() {
    final hasSearch = recipeSearchController.text.trim().isNotEmpty;
    final hasCuisineFilter = _selectedFilters['Cuisine']?.isNotEmpty ?? false;
    final hasCategoryFilter = _selectedFilters['Category']?.isNotEmpty ?? false;
    debugPrint(
        'Active filters - Search: $hasSearch, Cuisine: $hasCuisineFilter, Category: $hasCategoryFilter');
    return hasSearch || hasCuisineFilter || hasCategoryFilter;
  }

  Widget _buildRecipeList(dynamic recipeListState) {
    return Column(
      children: [
        SizedBox(height: 20.h),
        CustomSearchBar(
          width: DeviceUtils().isTabletOrIpad(context)
              ? 368
              : MediaQuery.of(context).size.width,
          controller: recipeSearchController,
          onClear: () => onClearFiltersPressed(context),
        ),
        Expanded(
          child: _buildRecipeListContent(recipeListState),
        ),
      ],
    );
  }

  Widget _buildRecipeListContent(dynamic recipeListState) {
    if (recipeListState.status == AppStatus.loading) {
      return const CookbookListShimmer(itemCount: 20);
    } else if (recipeListState.status == AppStatus.error ||
        recipeListState.data == null ||
        recipeListState.data!.isEmpty) {
      return const Center(
        child: NoDataWidget(
          title: "No Recipe Lists Found",
          subtitle:
              "Try adjusting your search terms or create a new recipe list",
          width: 250,
          height: 250,
        ),
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (scrollNotification) {
        if (scrollNotification is ScrollEndNotification) {
          final metrics = scrollNotification.metrics;
          if (metrics.pixels >= metrics.maxScrollExtent - 200 &&
              recipeListState.hasMore &&
              recipeListState.status != AppStatus.loadingMore) {
            ref.read(recipeNotifierProvider.notifier).fetchRecipes(
              cookbookId: _selectedCookbookId!,
              cuisineId: cuisine,
              categoryId: category,
              search: recipeSearchController.text.trim().isEmpty
                  ? null
                  : recipeSearchController.text.trim(),
              reset: false,
              context: context,
              currentPage: _currentPage + 1,
            );
            setState(() {
              _currentPage++;
            });
          }
        }
        return false;
      },
      child: ListView.separated(
        key: const PageStorageKey('recipe_list'),
        controller: _scrollController,
        padding: const EdgeInsets.all(12),
        itemCount:
            recipeListState.data!.length + (recipeListState.hasMore ? 1 : 0),
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          if (index >= recipeListState.data!.length) {
             return Center(
              child: LoadingAnimationWidget.fallingDot(
                color: Colors.grey,
                size: 50.0,
              ),
            );
          }
          final recipe = recipeListState.data![index];
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedRecipeIndex = index;
              _selectedRecipeId = recipe.id;
              selectedRecipeName = recipe.name;
              selectedMediaUrl = null; // Reset media URL
              selectedMediaFile = null; // Reset media file
              debugPrint(
                  'Selected recipe: $selectedRecipeName, index: $_selectedRecipeIndex');
            });
            scrollToSelectedRecipe(_selectedRecipeIndex);

            // Reset nutrition state before fetching new recipe data
            ref.read(nutritionInfoNotifierProvider.notifier).resetNutritionState();

            ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
                  context: context,
                  cookbookId: _selectedCookbookId ?? 0,
                  recipeId: _selectedRecipeId!,
                );
            ref.read(nutritionInfoNotifierProvider.notifier).getNutritions(
                  context: context,
                  recipeId: recipe.id,
                );
            if (MediaQuery.of(context).size.width < 1200) {
              Navigator.of(context).pop();
            }
          },
          child: RecipeCardItem(
            recipes: recipe,
            isSelected: _selectedRecipeIndex == index,
            isEditable: true,
            onMenuItemSelected: (value) async {
              if (value == 'Delete') {
                final recipeNotifier =
                    ref.read(recipeNotifierProvider.notifier);
                final isDeleting = recipeListState.status == AppStatus.deleting;
                if (!isDeleting && context.mounted) {
                  final isSuccess = await recipeNotifier.deleteRecipe(
                    context: context,
                    cookbookId: _selectedCookbookId ?? 0,
                    recipeId: recipe.id,
                  );
                  if (isSuccess) {
                    final updatedRecipeListState =
                        ref.read(recipeNotifierProvider);
                    if (updatedRecipeListState.data != null &&
                        updatedRecipeListState.data!.isNotEmpty) {
                      setState(() {
                        _selectedRecipeIndex = 0;
                        _selectedRecipeId = updatedRecipeListState.data![0].id;
                        selectedRecipeName =
                            updatedRecipeListState.data![0].name;
                        selectedMediaUrl = null;
                        selectedMediaFile = null;
                        debugPrint(
                            'Deleted recipe, new index: $_selectedRecipeIndex, recipeId: $_selectedRecipeId');
                      });
                      scrollToSelectedRecipe(_selectedRecipeIndex);
                      ref
                          .read(singleRecipeNotifierProvider.notifier)
                          .fetchRecipe(
                            context: context,
                            cookbookId: _selectedCookbookId ?? 0,
                            recipeId: _selectedRecipeId!,
                          );
                      ref
                          .read(nutritionInfoNotifierProvider.notifier)
                          .getNutritions(
                            context: context,
                            recipeId: widget.recipeId,
                          );
                    } else {
                      setState(() {
                        _selectedRecipeId = null;
                        selectedRecipeName = null;
                        selectedMediaUrl = null;
                        selectedMediaFile = null;
                        debugPrint('No recipes left after deletion');
                      });
                    }
                  }
                }
              }
            },
          ),
        );
      },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final recipeState = ref.watch(singleRecipeNotifierProvider);
    final categoriesState = ref.watch(categoriesNotifierProvider);
    final cuisinesState = ref.watch(cuisinesNotifierProvider);
    final recipeListState = ref.watch(recipeNotifierProvider);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width <= 1100;
    final nutritionDataState = ref.watch(nutritionInfoNotifierProvider);

    ref.listen(recipeNotifierProvider, (previous, next) {
      if (next.data != null && next.data!.isNotEmpty && context.mounted) {
        final index =
            next.data!.indexWhere((recipe) => recipe.id == _selectedRecipeId);
        if (index != -1 && index != _selectedRecipeIndex) {
          setState(() {
            _selectedRecipeIndex = index;
            _selectedRecipeId = next.data![index].id;
            selectedRecipeName = next.data![index].name;
            selectedMediaUrl = null;
            selectedMediaFile = null;
          });
          scrollToSelectedRecipe(_selectedRecipeIndex);
          ref.read(singleRecipeNotifierProvider.notifier).fetchRecipe(
                context: context,
                cookbookId: _selectedCookbookId ?? 0,
                recipeId: _selectedRecipeId!,
              );
        }
      }
    });

    ref.listen(singleRecipeNotifierProvider, (previous, next) {
      if (next.status == AppStatus.success && next.data != null) {
        final mediaList = next.data!.recipeMedia;
        if (mediaList != null && mediaList.isNotEmpty) {
          final firstMedia = mediaList.first;
          if (firstMedia.mediaFile != null) {
            // Prioritize local file if available (e.g., after update)
            if (selectedMediaFile != firstMedia.mediaFile) {
              setState(() {
                selectedMediaFile = firstMedia.mediaFile;
                selectedMediaUrl = null; // Clear URL if file is available
                debugPrint(
                    'Updated selectedMediaFile: ${firstMedia.mediaFile?.path}');
              });
            }
          } else if (firstMedia.mediaUrl != null &&
              selectedMediaUrl != firstMedia.mediaUrl) {
            // Fallback to URL if no file is available
            setState(() {
              selectedMediaUrl = firstMedia.mediaUrl;
              selectedMediaFile = null;
              debugPrint('Updated selectedMediaUrl: $selectedMediaUrl');
            });
          }
        } else {
          debugPrint('No valid media found in recipeMedia: $mediaList');
          setState(() {
            selectedMediaUrl = null;
            selectedMediaFile = null;
          });
        }
      }
    });

    ref.listen(mediaFilesProvider, (previous, next) {
      // Update media when mediaFilesProvider changes (e.g., from MediaPickerGrid)
      if (next.mediaFiles.isNotEmpty &&
          next.mediaFiles[0] != null &&
          next.mediaFiles[0]!.mediaType == 'VIDEO') {
        setState(() {
          selectedMediaFile = next.mediaFiles[0]!.mediaFile;
          selectedMediaUrl = null; // Clear URL to prioritize file
          debugPrint(
              'Media updated from mediaFilesProvider: ${next.mediaFiles[0]!.mediaFile?.path}');
        });
      }
    });

    return getDeviceType(context).name == 'mobile'
        ? RecipeDetailScreenMobile(
            recipeId: widget.recipeId,
            recipesList: widget.recipesList,
            selectedRecipeName: widget.selectedRecipeName,
            cookbookId: widget.cookbookId,
            cookBookName: widget.cookBookName,
          )
        : Scaffold(
            drawer: DeviceUtils().isTabletOrIpad(context)
                ? CustomDrawer(
                    title: 'Recipes',
                    state: recipeListState,
                    buildContent: _buildRecipeList,
                  )
                : null,
            appBar: CustomAppBar(
              title: widget.cookBookName ?? '',
              onPressed: () => Navigator.of(context).pop(),
              showDrawerIcon: DeviceUtils().isTabletOrIpad(context),
              actions: [
                if (!isSmallScreen)
                  Wrap(
                    spacing: 8,
                    children: _filterOptions.keys.map((filterName) {
                      return DropdownFilter(
                        filterName: filterName,
                        selectedFilters: _selectedFilters,
                        filterOptions: _filterOptions,
                        onFilterChanged: (filterName, selectedValue) {
                          setState(() {
                            _selectedFilters[filterName] = selectedValue;

                            switch (filterName) {
                              case 'Cuisine':
                                final cuisineModel = ref
                                    .read(cuisinesNotifierProvider)
                                    .data
                                    ?.firstWhere((c) => c.name == selectedValue,
                                        orElse: () => Cuisines());
                                cuisine = cuisineModel?.id ?? 0;
                                ref
                                    .read(recipeMetadataProvider.notifier)
                                    .updateCuisineId(cuisine);
                                ref
                                    .read(recipeNotifierProvider.notifier)
                                    .fetchRecipes(
                                      cookbookId: _selectedCookbookId!,
                                      cuisineId: cuisine,
                                      categoryId: category,
                                      reset: true,
                                      context: context,
                                      currentPage: 1,
                                    );
                                break;
                              case 'Category':
                                final categoryModel = ref
                                    .read(categoriesNotifierProvider)
                                    .data
                                    ?.firstWhere((c) => c.name == selectedValue,
                                        orElse: () => Categories());
                                category = categoryModel?.id ?? 0;
                                ref
                                    .read(recipeMetadataProvider.notifier)
                                    .updateCategoryId(category);
                                ref
                                    .read(recipeNotifierProvider.notifier)
                                    .fetchRecipes(
                                      cookbookId: _selectedCookbookId!,
                                      cuisineId: cuisine,
                                      categoryId: category,
                                      reset: true,
                                      context: context,
                                      currentPage: 1,
                                    );
                                break;
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),
                SizedBox(width: 12.w),
                if (isSmallScreen)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        size: 24,
                        color: const Color.fromARGB(
                            255, 147, 147, 147), // set the color if needed
                      ),
                      onPressed: () async {
                        final selectedFilters =
                            await showModalBottomSheet<Map<String, String>>(
                          context: context,
                          isScrollControlled: false,
                          shape: const RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(16)),
                          ),
                          builder: (context) => Padding(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).viewInsets.bottom,
                            ),
                            child: CookbookFilter(
                              initialFilters: _selectedFilters,
                              showSort: false,
                            ),
                          ),
                        );

                        if (selectedFilters != null) {
                          setState(() {
                            _selectedFilters = selectedFilters;
                          });

                          final cuisineName = selectedFilters['Cuisine'];
                          final categoryName = selectedFilters['Category'];

                          if (cuisineName != null && cuisineName.isNotEmpty) {
                            final cuisineModel = ref
                                .read(cuisinesNotifierProvider)
                                .data
                                ?.firstWhere((c) => c.name == cuisineName,
                                    orElse: () => Cuisines());
                            cuisine = cuisineModel?.id ?? 0;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCuisineId(cuisine);
                          } else {
                            cuisine = 0;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCuisineId(0);
                          }

                          if (categoryName != null && categoryName.isNotEmpty) {
                            final categoryModel = ref
                                .read(categoriesNotifierProvider)
                                .data
                                ?.firstWhere((c) => c.name == categoryName,
                                    orElse: () => Categories());
                            category = categoryModel?.id ?? 0;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCategoryId(category);
                          } else {
                            category = 0;
                            ref
                                .read(recipeMetadataProvider.notifier)
                                .updateCategoryId(0);
                          }

                          ref
                              .read(recipeNotifierProvider.notifier)
                              .fetchRecipes(
                                cookbookId: _selectedCookbookId!,
                                cuisineId: cuisine,
                                categoryId: category,
                                reset: true,
                                context: context,
                                currentPage: 1,
                              );
                        }
                      },
                      tooltip: 'Cookbooks',
                    ),
                  ),
                if (_hasActiveFilters())
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomButton(
                      text: "Clear Filter",
                      onPressed: () => onClearFiltersPressed(context),
                      fontSize:
                          DeviceUtils().isTabletOrIpad(context) ? 12 : 20.sp,
                      width: DeviceUtils().isTabletOrIpad(context) ? 90 : 200.w,
                    ),
                  ),
              ],
            ),
            body: Stack(
              fit: StackFit.expand,
              children: [
                Image.asset(
                  AssetsManager.background_img,
                  fit: BoxFit.cover,
                ),
                getDeviceType(context) == DeviceType.tablet
                    ? TabRecipeDetail(
                        recipeId: widget.recipeId,
                        cookbookId: widget.cookbookId,
                        selectedRecipeId: _selectedRecipeId ?? 0,
                        selectedCookbookId: _selectedCookbookId ?? 0,
                        recipesList: widget.recipesList,
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Container(
                              color: AppColors.secondaryColor,
                              child: _buildRecipeList(recipeListState),
                            ),
                          ),
                          Expanded(
                            flex: 8,
                            child: (recipeListState.data == null ||
                                    recipeListState.data!.isEmpty)
                                ? const Center(
                                    child: NoDataWidget(
                                      title: "No Recipe Detail Found",
                                      subtitle:
                                          "Try searching again or select a different recipe to view the details.",
                                      width: 250,
                                      height: 250,
                                    ),
                                  )
                                : Consumer(
                                    builder: (context, ref, _) {
                                      if (recipeState.status ==
                                          AppStatus.loading) {
                                        return RecipeDetailsShimmer();
                                      }

                                      if (recipeState.status ==
                                              AppStatus.error ||
                                          recipeState.data == null) {
                                        return NoDataWidget(
                                          title: "No Recipe Detail Found",
                                          subtitle:
                                              "Try searching again or select a different recipe to view the details.",
                                          width: 250,
                                          height: 250,
                                        );
                                      }

                                      final recipeDetail = recipeState.data!;

                                      return Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          leftMediaVideoWidget(
                                            context,
                                            recipeDetail,
                                            selectedMediaUrl,
                                            categoriesState.data,
                                            cuisinesState.data,
                                          //  widget.recipeId,
                                           _selectedRecipeId ?? 0,
                                            widget.cookbookId,
                                            widget.recipesList,
                                            ref: ref,
                                            nutritionDataState:
                                                nutritionDataState,
                                            onImageSelected:
                                                (String newImageUrl) {
                                              setState(() {
                                                selectedMediaUrl = newImageUrl;
                                                selectedMediaFile = null;
                                                debugPrint(
                                                    'Selected media URL: $newImageUrl');
                                              });
                                            },
                                          ),
                                          responsiveRightView(
                                              context: context,
                                              ingredients:
                                                  recipeDetail.ingredients,
                                              author: recipeDetail.author ?? '',
                                              authorMediaUrl:
                                                  recipeDetail.authorMediaUrl ??
                                                      '',
                                              copyright:
                                                  recipeDetail.copyright ?? '',
                                              source: recipeDetail.source ?? '',
                                              recipeDetail: recipeDetail,
                                              categoryList:
                                                  categoriesState.data,
                                              cusineList: cuisinesState.data,
                                              recipeId: _selectedRecipeId ?? 0,//widget.recipeId,
                                              cookbookId: widget.cookbookId,
                                              recipesList: widget.recipesList,
                                              selectedRecipeId:
                                                  _selectedRecipeId,
                                              selectedCookbookId:
                                                  _selectedCookbookId,
                                              ref: ref,
                                              isSmallScreen: false,
                                              nutritionDataState:
                                                  nutritionDataState),
                                        ],
                                      );
                                    },
                                  ),
                          ),
                        ],
                      )
              ],
            ),
          );
  }

  void onClearFiltersPressed(BuildContext context) {
    debugPrint('Clearing filters...');
    setState(() {
      cuisine = 0;
      category = 0;
      _selectedFilters['Cuisine'] = '';
      _selectedFilters['Category'] = '';
      recipeSearchController.clear();
      _currentPage = 1; // Reset pagination
    });

    ref.read(recipeMetadataProvider.notifier).resetFilters();
    ref.read(recipeNotifierProvider.notifier).fetchRecipes(
          cookbookId: _selectedCookbookId!,
          cuisineId: 0,
          categoryId: 0,
          search: null,
          reset: true,
          context: context,
          currentPage: 1,
        );
  }
}
