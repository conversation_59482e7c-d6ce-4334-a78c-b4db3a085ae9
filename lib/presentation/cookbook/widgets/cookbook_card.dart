import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../core/data/models/cookbook.dart';
import '../../../core/providers/cookbook_notifier.dart';
import '../../../core/utils/screen_sizer.dart';
import '../../../core/widgets/camera_upload_image.dart';
import '../../../core/widgets/common_image.dart';
import '../../../core/widgets/cookbook_cover_image.dart';
import '../../../core/widgets/custom_hover_menu.dart';
import '../../../core/widgets/custom_popup_menu.dart';

class CookbookCard extends ConsumerWidget {
  final Cookbook cookbook;
  final bool isHighRes;
  final VoidCallback onTap;

  const CookbookCard({
    super.key,
    required this.cookbook,
    required this.isHighRes,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenSize = MediaQuery.of(context).size;
    final cookbookNotifier = ref.read(cookbookNotifierProvider.notifier);
    final cookbookState = ref.watch(cookbookNotifierProvider);

    // Show loading indicator when deleting
    final isDeleting = cookbookState.status == AppStatus.loading;

    return Card(
      color: context.theme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          // left: screenSize.width *
          //     (DeviceUtils().isTabletOrIpad(context) ? 0.01 : 0.005),
          // right: screenSize.width *
          //     (DeviceUtils().isTabletOrIpad(context) ? 0.01 : 0.005),
          // top: screenSize.width *
          //     (DeviceUtils().isTabletOrIpad(context) ? 0.01 : 0.004),

          left: screenSize.width * 0.005,
          right: screenSize.width * 0.005,
          top: screenSize.width * 0.005,
        ),
        child: // Inside CookbookCard's build method
            // Inside CookbookCard's build method
            Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                CookbookCoverImage(
                  cookbook: cookbook,
                  height: ScreenSizer().calculateImageHeight(context),
                  width: screenSize.width,
                  fit: BoxFit.cover,
                  placeholder: AssetsManager.cb_place_holder,
                  borderRadius: 15,
                ),
                cameraUploadImage(onTap: () {
                  cookbookNotifier.pickImage(
                      context, ref, cookbook.id.toString(), cookbook.name);
                })
              ],
            ),
            SizedBox(height: 16.h),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        cookbook.name,
                        style: context.theme.textTheme.labelSmall!.copyWith(
                          color: AppColors.primaryGreyColor,
                          fontSize: DeviceUtils().isTabletOrIpad(context)
                              ? 14
                              : responsiveFont(26).sp,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 15.h),
                    CustomHoverMenu(
                      items: ['Edit', 'Delete'],
                      itemIcons: [Icons.edit, Icons.delete],
                      // Icons for each item
                      onItemSelected: (value) {
                        if (value == 'Edit') {
                          onTap();
                        } else if (value == 'Delete') {
                          if (!isDeleting) {
                            cookbookNotifier.deleteCookbook(
                                context, cookbook.id.toString());
                          }
                        }
                      },
                      menuWidth: 140.0,
                      // Matches previous menu width
                      menuTitle: 'Show Menu',
                      // Tooltip on hover
                      triggerIcon:
                          Icons.more_vert_outlined, // Custom trigger icon
                    ),
                    // Removed redundant Edit and Delete buttons since they are now in the menu
                  ],
                ),
                SizedBox(
                    height: DeviceUtils().isTabletOrIpad(context) ? 4 : 16.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${cookbook.recipeCount} recipes',
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        color: AppColors.textGreyColor,
                        fontSize:
                            DeviceUtils().isTabletOrIpad(context) ? 12 : 22.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      'Created : ${timeago.format(cookbook.dateAdded, locale: 'en_short').replaceAll('just now ago', 'just now')}',
                      style: context.theme.textTheme.labelSmall!.copyWith(
                        color: AppColors.textGreyColor,
                        fontSize:
                            DeviceUtils().isTabletOrIpad(context) ? 10 : 20.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                CustomButton(
                  text: 'Open Cookbook',
                  fontSize: DeviceUtils().isTabletOrIpad(context) ? 14 : 20.sp,
                  onPressed: () {
                    if (!isDeleting) {
                      context.go(
                          '/cookbook/cookbookDetail?id=${cookbook.id}&name=${Uri.encodeComponent(cookbook.name)}');
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
