/// Utility class for version comparison
class VersionUtils {
  /// Compare two version strings
  /// Returns:
  /// - negative number if version1 < version2
  /// - 0 if version1 == version2
  /// - positive number if version1 > version2
  static int compareVersions(String version1, String version2) {
    // Remove any non-numeric characters except dots
    String cleanVersion1 = version1.replaceAll(RegExp(r'[^0-9.]'), '');
    String cleanVersion2 = version2.replaceAll(RegExp(r'[^0-9.]'), '');
    
    List<int> v1Parts = cleanVersion1.split('.').map((e) => int.tryParse(e) ?? 0).toList();
    List<int> v2Parts = cleanVersion2.split('.').map((e) => int.tryParse(e) ?? 0).toList();
    
    // Make both lists the same length by padding with zeros
    int maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;
    
    while (v1Parts.length < maxLength) {
      v1Parts.add(0);
    }
    while (v2Parts.length < maxLength) {
      v2Parts.add(0);
    }
    
    // Compare each part
    for (int i = 0; i < maxLength; i++) {
      if (v1Parts[i] < v2Parts[i]) {
        return -1;
      } else if (v1Parts[i] > v2Parts[i]) {
        return 1;
      }
    }
    
    return 0;
  }
  
  /// Check if current version is less than API version
  static bool isUpdateRequired(String currentVersion, String apiVersion) {
    return compareVersions(currentVersion, apiVersion) < 0;
  }
}
