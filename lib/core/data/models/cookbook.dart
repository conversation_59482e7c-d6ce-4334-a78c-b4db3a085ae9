import 'package:flutter/material.dart';

class PostCookbookResponse {
  final bool success;
  final Message message;
  final PostCookbookResponseData data;
  final int status;

  PostCookbookResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory PostCookbookResponse.fromJson(Map<String, dynamic> json) {
    return PostCookbookResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: PostCookbookResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class Message {
  final List<String>? error;
  final List<String>? general;

  Message({this.error, this.general});

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      error: (json['error'] as List?)?.map((e) => e.toString()).toList(),
      general: (json['general'] as List?)?.map((e) => e.toString()).toList(),
    );
  }
}

class PostCookbookResponseData {
  final int cookbookId;

  PostCookbookResponseData({
    required this.cookbookId,
  });

  factory PostCookbookResponseData.fromJson(Map<String, dynamic> json) {
    return PostCookbookResponseData(
      cookbookId: json['cookbookId'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cookbookId': cookbookId,
    };
  }
}

class GetCookbooksResponse {
  final bool success;
  final Message message;
  final GetCookbooksResponseData data;
  final int status;

  GetCookbooksResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory GetCookbooksResponse.fromJson(Map<String, dynamic> json) {
    return GetCookbooksResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      data: GetCookbooksResponseData.fromJson(json['data']),
      status: json['status'] as int,
    );
  }
}

class GetCookbooksResponseData {
  final List<Cookbook> cookbooks;
  final int totalRecords;
  final int totalPageCount;

  GetCookbooksResponseData({
    required this.cookbooks,
    required this.totalRecords,
    required this.totalPageCount,
  });

  factory GetCookbooksResponseData.fromJson(Map<String, dynamic> json) {
    // Handle potential parsing errors in cookbook list
    List<Cookbook> cookbooks = [];
    if (json['cookbooks'] != null) {
      final cookbookList = json['cookbooks'] as List;
      for (var cookbookJson in cookbookList) {
        try {
          cookbooks.add(Cookbook.fromJson(cookbookJson));
        } catch (e) {
          debugPrint('Error parsing cookbook: $e');
          debugPrint('Cookbook JSON: $cookbookJson');
          // Continue with other cookbooks even if one fails to parse
        }
      }
    }

    return GetCookbooksResponseData(
      cookbooks: cookbooks,
      totalRecords: json['totalRecords'] as int,
      totalPageCount: json['totalPageCount'] as int,
    );
  }
}

class CookbookRecipeMedia {
  final String url;

  CookbookRecipeMedia({
    required this.url,
  });

  factory CookbookRecipeMedia.fromJson(Map<String, dynamic> json) {
    return CookbookRecipeMedia(
      url: json['mediaUrl'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mediaUrl': url,
    };
  }
}

class Cookbook {
  final int id;
  final String name;
  final dynamic user;
  final int recipeCount;
  final dynamic recipes;
  final dynamic userDesktopMaps;
  final bool system;
  final String? description;
  final DateTime dateAdded;
  final DateTime lastModified;
  final dynamic coverImage;
  final String? coverImageUrl;
  final List<CookbookRecipeMedia>? recipeMedia;
  final bool fromStore;
  final dynamic sku;
  final String type;
  final dynamic groupShare;
  final dynamic contentCollection;
  final bool readOnly;
  final String permission;

  Cookbook({
    required this.id,
    required this.name,
    this.user,
    required this.recipeCount,
    this.recipes,
    this.userDesktopMaps,
    required this.system,
    this.description,
    required this.dateAdded,
    required this.lastModified,
    this.coverImage,
    this.coverImageUrl,
    this.recipeMedia,
    required this.fromStore,
    this.sku,
    required this.type,
    this.groupShare,
    this.contentCollection,
    required this.readOnly,
    required this.permission,
  });

  factory Cookbook.fromJson(Map<String, dynamic> json) {
    // Handle malformed API response where coverImageUrl might be missing or malformed
    String? coverImageUrl;

    // First try to get coverImageUrl normally
    if (json.containsKey('coverImageUrl')) {
      coverImageUrl = json['coverImageUrl'] as String?;
    } else {
      // If coverImageUrl is not present, look for any URL-like string in the JSON values
      // This handles the malformed API response where URL appears without a key
      for (var entry in json.entries) {
        if (entry.value is String &&
            entry.value.toString().startsWith('http') &&
            entry.value.toString().contains('amazonaws.com')) {
          coverImageUrl = entry.value as String;
          break;
        }
      }
    }

    // Parse recipeMedia array
    List<CookbookRecipeMedia>? recipeMedia;
    if (json['recipeMedia'] != null && json['recipeMedia'] is List) {
      recipeMedia = (json['recipeMedia'] as List)
          .map((item) => CookbookRecipeMedia.fromJson(item))
          .toList();
    }

    return Cookbook(
      id: json['id'] as int,
      name: json['name'] as String,
      user: json['user'],
      recipeCount: json['recipeCount'] as int,
      recipes: json['recipes'],
      userDesktopMaps: json['userDesktopMaps'],
      system: json['system'] as bool,
      description: json['description'],
      dateAdded: DateTime.parse(json['dateAdded'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
      coverImage: json['coverImage'],
      coverImageUrl: coverImageUrl,
      recipeMedia: recipeMedia,
      fromStore: json['fromStore'] as bool,
      sku: json['sku'],
      type: json['type'] as String,
      groupShare: json['groupShare'],
      contentCollection: json['contentCollection'],
      readOnly: json['readOnly'] as bool,
      permission: json['permission'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'user': user,
      'recipeCount': recipeCount,
      'recipes': recipes,
      'userDesktopMaps': userDesktopMaps,
      'system': system,
      'description': description,
      'dateAdded': dateAdded.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'coverImage': coverImage,
      'coverImageUrl': coverImageUrl,
      'recipeMedia': recipeMedia?.map((item) => item.toJson()).toList(),
      'fromStore': fromStore,
      'sku': sku,
      'type': type,
      'groupShare': groupShare,
      'contentCollection': contentCollection,
      'readOnly': readOnly,
      'permission': permission,
    };
  }
}

class DeleteCookbookListResponse {
  final bool success;
  final Message message;
  final int status;

  DeleteCookbookListResponse({
    required this.success,
    required this.message,
    required this.status,
  });

  factory DeleteCookbookListResponse.fromJson(Map<String, dynamic> json) {
    return DeleteCookbookListResponse(
      success: json['success'] as bool,
      message: Message.fromJson(json['message']),
      status: json['status'] as int,
    );
  }
}
