class AppUpdateResponse {
  final bool success;
  final AppUpdateMessage message;
  final AppUpdateData data;
  final int status;

  AppUpdateResponse({
    required this.success,
    required this.message,
    required this.data,
    required this.status,
  });

  factory AppUpdateResponse.fromJson(Map<String, dynamic> json) {
    return AppUpdateResponse(
      success: json['success'] ?? false,
      message: AppUpdateMessage.fromJson(json['message'] ?? {}),
      data: AppUpdateData.fromJson(json['data'] ?? {}),
      status: json['status'] ?? 0,
    );
  }
}

class AppUpdateMessage {
  final List<String>? error;
  final List<String>? general;

  AppUpdateMessage({this.error, this.general});

  factory AppUpdateMessage.fromJson(Map<String, dynamic> json) {
    return AppUpdateMessage(
      error: json['error']?.cast<String>(),
      general: json['general']?.cast<String>(),
    );
  }
}

class AppUpdateData {
  final AppVersions appVersions;

  AppUpdateData({required this.appVersions});

  factory AppUpdateData.fromJson(Map<String, dynamic> json) {
    return AppUpdateData(
      appVersions: AppVersions.fromJson(json['appVersions'] ?? {}),
    );
  }
}

class AppVersions {
  final bool isForce;
  final String iosVersion;
  final String macosVersion;
  final String androidVersion;
  final String windowsVersion;
  final String linuxVersion;
  final String ubuntuVersion;
  final String chromebookVersion;

  AppVersions({
    required this.isForce,
    required this.iosVersion,
    required this.macosVersion,
    required this.androidVersion,
    required this.windowsVersion,
    required this.linuxVersion,
    required this.ubuntuVersion,
    required this.chromebookVersion,
  });

  factory AppVersions.fromJson(Map<String, dynamic> json) {
    return AppVersions(
      isForce: json['isForce'] ?? false,
      iosVersion: json['iosVersion'] ?? '',
      macosVersion: json['macosVersion'] ?? '',
      androidVersion: json['androidVersion'] ?? '',
      windowsVersion: json['windowsVersion'] ?? '',
      linuxVersion: json['linuxVersion'] ?? '',
      ubuntuVersion: json['ubuntuVersion'] ?? '',
      chromebookVersion: json['chromebookVersion'] ?? '',
    );
  }
}
