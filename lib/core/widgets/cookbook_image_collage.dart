import 'package:flutter/material.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/widgets/common_image.dart';

class CookbookImageCollage extends StatelessWidget {
  final List<CookbookRecipeMedia> recipeMedia;
  final double height;
  final double width;
  final BoxFit fit;
  final String? placeholder;

  const CookbookImageCollage({
    super.key,
    required this.recipeMedia,
    required this.height,
    required this.width,
    this.fit = BoxFit.cover,
    this.placeholder,
  });

  @override
  Widget build(BuildContext context) {
    // If no media, show placeholder
    if (recipeMedia.isEmpty) {
      return _buildPlaceholder();
    }

    // Take up to 4 images
    final images = recipeMedia.take(4).toList();
    
    if (images.length == 1) {
      return _buildSingleImage(images[0]);
    } else if (images.length == 2) {
      return _buildTwoImages(images);
    } else if (images.length == 3) {
      return _buildThreeImages(images);
    } else {
      return _buildFourImages(images);
    }
  }

  Widget _buildPlaceholder() {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: CommonImage(
        imageSource: placeholder ?? AssetsManager.cb_place_holder,
        height: height,
        width: width,
        fit: fit,
      ),
    );
  }

  Widget _buildSingleImage(CookbookRecipeMedia media) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CommonImage(
          imageSource: media.url,
          placeholder: placeholder ?? AssetsManager.cb_place_holder,
          height: height,
          width: width,
          fit: fit,
        ),
      ),
    );
  }

  Widget _buildTwoImages(List<CookbookRecipeMedia> images) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            Expanded(
              child: CommonImage(
                imageSource: images[0].url,
                placeholder: placeholder ?? AssetsManager.cb_place_holder,
                height: height,
                width: width / 2,
                fit: fit,
              ),
            ),
            const SizedBox(width: 1),
            Expanded(
              child: CommonImage(
                imageSource: images[1].url,
                placeholder: placeholder ?? AssetsManager.cb_place_holder,
                height: height,
                width: width / 2,
                fit: fit,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreeImages(List<CookbookRecipeMedia> images) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: CommonImage(
                imageSource: images[0].url,
                placeholder: placeholder ?? AssetsManager.cb_place_holder,
                height: height,
                width: width * 0.67,
                fit: fit,
              ),
            ),
            const SizedBox(width: 1),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: CommonImage(
                      imageSource: images[1].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width * 0.33,
                      fit: fit,
                    ),
                  ),
                  const SizedBox(height: 1),
                  Expanded(
                    child: CommonImage(
                      imageSource: images[2].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width * 0.33,
                      fit: fit,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFourImages(List<CookbookRecipeMedia> images) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: CommonImage(
                      imageSource: images[0].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width / 2,
                      fit: fit,
                    ),
                  ),
                  const SizedBox(width: 1),
                  Expanded(
                    child: CommonImage(
                      imageSource: images[1].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width / 2,
                      fit: fit,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 1),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: CommonImage(
                      imageSource: images[2].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width / 2,
                      fit: fit,
                    ),
                  ),
                  const SizedBox(width: 1),
                  Expanded(
                    child: CommonImage(
                      imageSource: images[3].url,
                      placeholder: placeholder ?? AssetsManager.cb_place_holder,
                      height: height / 2,
                      width: width / 2,
                      fit: fit,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
