import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/utils/device_utils.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';
import '../../app/imports/packages_imports.dart';

class AppUpdateDialog extends ConsumerWidget {
  final bool hideLoader;
  final bool hideCloseButton;

  const AppUpdateDialog({
    super.key,
    this.hideLoader = false,
    this.hideCloseButton = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: getDeviceType(context).name == 'mobile'
            ? 0
            : getDeviceType(context).name == 'tablet'
                ? MediaQuery.of(context).size.width * 0.17
                : MediaQuery.of(context).size.width * 0.3,
      ),
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.white,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Illustration / Icon
                  Image.asset(
                    AssetsManager.appUpdate, // replace with your update asset
                    height: 150,
                  ),
                  const SizedBox(height: 20),

                  // Title
                  CustomText(
                    text: "Update Available",
                    size: getDeviceType(context).name == 'mobile' ? 18 : 24,
                    weight: FontWeight.bold,
                    color: AppColors.blackTextColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 10),

                  // Description
                  const CustomText(
                    text:
                        "A new version of the app is here. Upgrade today for the best performance.",
                    size: 14,
                    weight: FontWeight.w400,
                    color: AppColors.textGreyColor,
                    align: TextAlign.center,
                  ),
                  const SizedBox(height: 20),

                  // Loader (optional)
                  if (!hideLoader) ...[
                    const CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primaryColor,
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Buttons
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 35.0),
                    child: Column(
                      children: [
                        CustomButton(
                          text: "Update Now",
                          onPressed: () {
                            Navigator.pop(context, "update");
                            // TODO: add store link here
                          },
                          borderRadius: 8,
                        ),
                        const SizedBox(height: 10),
                        CustomButton(
                          text: "Later",
                          onPressed: () => Navigator.pop(context, "later"),
                          borderRadius: 8,
                          textColor: Colors.black,
                          color: AppColors.greyBorderColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Close icon (optional)
            if (!hideCloseButton)
              Positioned(
                top: 10,
                right: 10,
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: AppColors.textGreyColor,
                    size: 24,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Show app update dialog
Future<String?> showAppUpdateDialog(
  BuildContext context, {
  bool hideLoader = false,
  bool hideCloseButton = false,
}) {
  return showDialog<String>(
    context: context,
    barrierDismissible: !hideCloseButton,
    builder: (context) => AppUpdateDialog(
      hideLoader: hideLoader,
      hideCloseButton: hideCloseButton,
    ),
  );
} //    await showAppUpdateDialog(context, hideLoader: true, hideCloseButton: true);
