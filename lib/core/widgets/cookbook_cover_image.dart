import 'package:flutter/material.dart';
import 'package:mastercookai/app/assets_manager.dart';
import 'package:mastercookai/core/data/models/cookbook.dart';
import 'package:mastercookai/core/widgets/common_image.dart';
import 'package:mastercookai/core/widgets/cookbook_image_collage.dart';

class CookbookCoverImage extends StatelessWidget {
  final Cookbook cookbook;
  final double height;
  final double width;
  final BoxFit fit;
  final String? placeholder;
  final double? borderRadius;
  final Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder;

  const CookbookCoverImage({
    super.key,
    required this.cookbook,
    required this.height,
    required this.width,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.borderRadius,
    this.loadingBuilder,
  });

  @override
  Widget build(BuildContext context) {
    // Check if coverImageUrl is available and not null/empty
    if (cookbook.coverImageUrl != null && cookbook.coverImageUrl!.isNotEmpty) {
      return _buildCoverImage();
    }
    
    // Fallback to collage if recipeMedia is available
    if (cookbook.recipeMedia != null && cookbook.recipeMedia!.isNotEmpty) {
      return _buildCollage();
    }
    
    // Final fallback to placeholder
    return _buildPlaceholder();
  }

  Widget _buildCoverImage() {
    Widget imageWidget = CommonImage(
      imageSource: cookbook.coverImageUrl,
      placeholder: placeholder ?? AssetsManager.cb_place_holder,
      height: height,
      width: width,
      fit: fit,
      loadingBuilder: loadingBuilder,
    );

    return borderRadius != null
        ? ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius!),
            child: imageWidget,
          )
        : imageWidget;
  }

  Widget _buildCollage() {
    Widget collageWidget = CookbookImageCollage(
      recipeMedia: cookbook.recipeMedia!,
      height: height,
      width: width,
      fit: fit,
      placeholder: placeholder,
    );

    return borderRadius != null
        ? ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius!),
            child: collageWidget,
          )
        : collageWidget;
  }

  Widget _buildPlaceholder() {
    Widget placeholderWidget = CommonImage(
      imageSource: placeholder ?? AssetsManager.cb_place_holder,
      height: height,
      width: width,
      fit: fit,
    );

    return borderRadius != null
        ? ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius!),
            child: placeholderWidget,
          )
        : placeholderWidget;
  }
}
