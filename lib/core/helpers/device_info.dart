import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform, NetworkInterface, InternetAddressType;
import '../models/app_update_response.dart';

class DeviceInfoService {
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();

  static Future<String> getDeviceName() async {
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await _deviceInfoPlugin.webBrowserInfo;
      debugPrint('Running on ${webBrowserInfo.userAgent}');
      return webBrowserInfo.browserName.name ?? "Unknown Web Device";
    }

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
      return androidInfo.model ?? "Unknown Android Device";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
      return iosInfo.utsname.machine ?? "Unknown iOS Device";
    }
    return "Unknown Device";
  }

  static Future<String> getDeviceId() async {
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await _deviceInfoPlugin.webBrowserInfo;
      debugPrint('Running on ${webBrowserInfo.userAgent}');
      return webBrowserInfo.browserName.name ?? "Unknown Web id";
    }

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
      return androidInfo.id ?? "unknown_android_id";
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
      return iosInfo.identifierForVendor ?? "unknown_ios_id";
    }
    return "unknown_device_id";
  }

  static Future<String> getIpAddress() async {
    if (kIsWeb) return "web_ip_unknown";

    try {
      for (var interface in await NetworkInterface.list(
        includeLinkLocal: true,
        type: InternetAddressType.IPv4,
      )) {
        for (var addr in interface.addresses) {
          if (!addr.isLoopback) {
            return addr.address;
          }
        }
      }
    } catch (e) {
      return 'Failed to get IP: $e';
    }

    return 'IP not found';
  }

  static Future<Map<String, String>> getDeviceInfo() async {
    final name = await getDeviceName();
    final id = await getDeviceId();
    return {
      "device_name": name,
      "device_id": id,
    };
  }

  static String getCurrentPlatformVersion(AppVersions versions) {
    if (kIsWeb) {
      return versions.chromebookVersion;
    }

    if (Platform.isAndroid) {
      return versions.androidVersion;
    } else if (Platform.isIOS) {
      return versions.iosVersion;
    } else if (Platform.isMacOS) {
      return versions.macosVersion;
    } else if (Platform.isWindows) {
      return versions.windowsVersion;
    } else if (Platform.isLinux) {
      return versions.linuxVersion;
    }

    return "Unknown Version";
  }

  static String getCurrentPlatformName() {
    if (kIsWeb) {
      return "Web";
    }

    if (Platform.isAndroid) {
      return "Android";
    } else if (Platform.isIOS) {
      return "iOS";
    } else if (Platform.isMacOS) {
      return "macOS";
    } else if (Platform.isWindows) {
      return "Windows";
    } else if (Platform.isLinux) {
      return "Linux";
    }
    return "Unknown";
  }
}
